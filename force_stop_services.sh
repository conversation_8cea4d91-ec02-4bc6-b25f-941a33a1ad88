#!/bin/bash

# 强制停止所有相关服务

echo "🛑 强制停止所有服务..."

# 1. 查找并强制停止8080端口进程
echo "查找8080端口进程..."
PORT_8080_PIDS=$(sudo netstat -tlnp | grep ":8080 " | awk '{print $7}' | cut -d'/' -f1 | grep -v '-')
if [ -n "$PORT_8080_PIDS" ]; then
    echo "发现8080端口进程: $PORT_8080_PIDS"
    for pid in $PORT_8080_PIDS; do
        echo "强制停止进程: $pid"
        sudo kill -9 $pid 2>/dev/null
    done
else
    echo "未找到8080端口进程，尝试其他方法..."
    sudo fuser -k 8080/tcp 2>/dev/null || echo "fuser命令失败"
fi

# 2. 查找并强制停止8081端口进程
echo "查找8081端口进程..."
PORT_8081_PIDS=$(sudo netstat -tlnp | grep ":8081 " | awk '{print $7}' | cut -d'/' -f1 | grep -v '-')
if [ -n "$PORT_8081_PIDS" ]; then
    echo "发现8081端口进程: $PORT_8081_PIDS"
    for pid in $PORT_8081_PIDS; do
        echo "强制停止进程: $pid"
        sudo kill -9 $pid 2>/dev/null
    done
else
    echo "未找到8081端口进程，尝试其他方法..."
    sudo fuser -k 8081/tcp 2>/dev/null || echo "fuser命令失败"
fi

# 3. 停止所有Python相关进程
echo "停止所有相关Python进程..."
sudo pkill -9 -f "python.*8080" 2>/dev/null
sudo pkill -9 -f "python.*8081" 2>/dev/null
sudo pkill -9 -f "uvicorn" 2>/dev/null
sudo pkill -9 -f "real_video_server" 2>/dev/null
sudo pkill -9 -f "Demo.py" 2>/dev/null
sudo pkill -9 -f "server.py" 2>/dev/null

# 4. 等待进程完全停止
echo "等待进程停止..."
sleep 3

# 5. 最终检查
echo "最终检查端口状态..."
if sudo netstat -tlnp | grep ":8080 " >/dev/null 2>&1; then
    echo "❌ 端口8080仍被占用"
    sudo netstat -tlnp | grep ":8080 "
else
    echo "✅ 端口8080已释放"
fi

if sudo netstat -tlnp | grep ":8081 " >/dev/null 2>&1; then
    echo "❌ 端口8081仍被占用"
    sudo netstat -tlnp | grep ":8081 "
else
    echo "✅ 端口8081已释放"
fi

echo "🎉 强制停止完成！"
