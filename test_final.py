import json
from execute_engine_rules_final import execute_engine_rules

# 测试数据
test_content_data = {
    'analysis_result': {
        'ocr_result': ['众安贷', '有钱花', '微粒贷'],
        'asr_result': ['推荐使用有钱花，比微粒贷更好用']
    }
}

test_rules = [
    {
        'business_category': '信贷业务',
        'violation_template': '违规：使用竞品词 #{keywords}，涉嫌侵权行为。',
        'rule_name': '禁止出现竞品词',
        'rule_category': '公司',
        'engine_config': '{"text_sources": "all", "match_logic": "OR", "case_sensitive": false, "threshold": {"confidence": 0.9}}',
        'priority': 'HIGH',
        '编号': '953c45a7-052a-4a61-b07d-e54d88bc4888',
        'engine_type': 'keyword_logic',
        'rule_description': '禁止在广告内容中出现指定的竞品公司或产品名称，避免侵权行为和不正当竞争',
        'engine_rule': '{"logic": "OR", "conditions": [{"type": "contains", "keywords": ["信用飞", "洋钱罐", "网商贷", "花鸭借钱", "海尔金融", "放心借", "招连金融", "哈罗app借款", "小辉付", "小花借款", "你我贷", "全民钱包", "乐分期", "小赢卡贷", "苏宁易购金融", "马上消费金融", "中原消费金融", "有钱花", "拍拍贷", "微粒贷", "好分期", "度小满", "极融", "借条", "马上消金", "中原消费", "中原消金", "还呗"], "match_type": "exact", "violation_template": "违规：使用竞品词\\"{keyword}\\"，涉嫌侵权行为。"}]}',
        'audit_type': 'ENGINE',
        'status': True
    }
]

result = execute_engine_rules(test_content_data, test_rules)
print(json.dumps(result, indent=2, ensure_ascii=False))
